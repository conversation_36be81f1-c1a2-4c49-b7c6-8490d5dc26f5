package com.pgs.dto;

import com.pgs.dto.user.CreateUserRequest;
import com.pgs.dto.user.UpdateUserRequest;
import com.pgs.dto.user.UserResponse;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class UserDtoTest {

    @Test
    void shouldCreateUserRequestWithUsernameAndNickname() {
        // Given & When
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setNickname("Test User");
        request.setPassword("password123");
        request.setRoles(Set.of("USER"));

        // Then
        assertEquals("testuser", request.getUsername());
        assertEquals("Test User", request.getNickname());
        assertEquals("password123", request.getPassword());
        assertEquals(Set.of("USER"), request.getRoles());
    }

    @Test
    void shouldCreateUpdateUserRequestWithNickname() {
        // Given & When
        UpdateUserRequest request = new UpdateUserRequest();
        request.setNickname("Updated Nickname");
        request.setRoles(Set.of("ADMIN"));
        request.setIsActive(false);

        // Then
        assertEquals("Updated Nickname", request.getNickname());
        assertEquals(Set.of("ADMIN"), request.getRoles());
        assertFalse(request.getIsActive());
    }

    @Test
    void shouldCreateUserResponseWithNickname() {
        // Given & When
        UserResponse response = UserResponse.builder()
            .userId(UUID.randomUUID())
            .username("testuser")
            .nickname("Test User")
            .isActive(true)
            .twoFactorEnabled(false)
            .roles(Set.of("USER"))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Then
        assertNotNull(response.getUserId());
        assertEquals("testuser", response.getUsername());
        assertEquals("Test User", response.getNickname());
        assertTrue(response.getIsActive());
        assertFalse(response.getTwoFactorEnabled());
        assertEquals(Set.of("USER"), response.getRoles());
        assertNotNull(response.getCreatedAt());
        assertNotNull(response.getUpdatedAt());
    }

    @Test
    void shouldNotHaveEmailFieldInDtos() {
        // This test verifies that email field doesn't exist in any of the DTOs
        // If email field existed, this test would fail at compilation
        
        CreateUserRequest createRequest = new CreateUserRequest();
        createRequest.setUsername("test");
        createRequest.setNickname("Test");
        createRequest.setPassword("password");
        createRequest.setRoles(Set.of("USER"));
        
        UpdateUserRequest updateRequest = new UpdateUserRequest();
        updateRequest.setNickname("Updated");
        
        UserResponse response = UserResponse.builder()
            .userId(UUID.randomUUID())
            .username("test")
            .nickname("Test")
            .isActive(true)
            .twoFactorEnabled(false)
            .roles(Set.of("USER"))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
        
        // All DTOs should be created successfully without email field
        assertNotNull(createRequest);
        assertNotNull(updateRequest);
        assertNotNull(response);
    }
}
