package com.pgs.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UserEntityTest {

    @Test
    void shouldCreateUserWithUsernameAndNickname() {
        // Given & When
        User user = User.builder()
            .username("testuser")
            .nickname("Test User")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // Then
        assertNotNull(user);
        assertEquals("testuser", user.getUsername());
        assertEquals("Test User", user.getNickname());
        assertEquals("hashedPassword", user.getPasswordHash());
        assertTrue(user.getIsActive());
    }

    @Test
    void shouldAllowNicknameUpdate() {
        // Given
        User user = User.builder()
            .username("testuser")
            .nickname("Original Nickname")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // When
        user.setNickname("Updated Nickname");

        // Then
        assertEquals("Updated Nickname", user.getNickname());
        assertEquals("testuser", user.getUsername()); // Username should remain unchanged
    }

    @Test
    void shouldNotHaveEmailField() {
        // Given
        User user = User.builder()
            .username("testuser")
            .nickname("Test User")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // Then - This test verifies that email field doesn't exist
        // If email field existed, this test would fail at compilation
        assertNotNull(user);
        assertEquals("testuser", user.getUsername());
        assertEquals("Test User", user.getNickname());
    }

    @Test
    void shouldHaveUniqueNicknameConstraint() {
        // Given
        User user1 = User.builder()
            .username("testuser1")
            .nickname("Unique Nickname")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        User user2 = User.builder()
            .username("testuser2")
            .nickname("Unique Nickname") // Same nickname as user1
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // Then - Both users can be created in memory, but database constraint will prevent duplicates
        assertNotNull(user1);
        assertNotNull(user2);
        assertEquals("Unique Nickname", user1.getNickname());
        assertEquals("Unique Nickname", user2.getNickname());
        // Note: Database constraint will prevent saving both users with same nickname
    }
}
