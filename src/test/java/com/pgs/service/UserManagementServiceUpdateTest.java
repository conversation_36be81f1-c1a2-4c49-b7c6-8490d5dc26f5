package com.pgs.service;

import com.pgs.dto.user.UpdateUserRequest;
import com.pgs.dto.user.UserResponse;
import com.pgs.entity.Role;
import com.pgs.entity.User;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.RoleRepository;
import com.pgs.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserManagementServiceUpdateTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private AuditService auditService;

    @Mock
    private TwoFactorAuthService twoFactorAuthService;

    @InjectMocks
    private UserManagementService userManagementService;

    private User user;
    private UUID userId;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        user = User.builder()
            .username("testuser")
            .nickname("Original Nickname")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();
        user.setId(userId);
    }

    @Test
    void shouldUpdateNicknameSuccessfully() {
        // Given
        UpdateUserRequest request = new UpdateUserRequest();
        request.setNickname("Updated Nickname");

        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(userRepository.existsByNickname("Updated Nickname")).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(twoFactorAuthService.isTwoFactorEnabled(any(User.class))).thenReturn(false);

        // When
        UserResponse response = userManagementService.updateUser(userId, request);

        // Then
        assertNotNull(response);
        assertEquals("testuser", response.getUsername());
        assertEquals("Updated Nickname", user.getNickname());
        verify(userRepository).save(user);
        verify(auditService).logUserAction(any(), eq("USER_UPDATED"), eq("USER"), eq(userId), anyString());
    }

    @Test
    void shouldNotUpdateWhenNicknameIsEmpty() {
        // Given
        UpdateUserRequest request = new UpdateUserRequest();
        request.setNickname("   ");
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(twoFactorAuthService.isTwoFactorEnabled(any(User.class))).thenReturn(false);

        // When
        UserResponse response = userManagementService.updateUser(userId, request);

        // Then
        assertNotNull(response);
        assertEquals("Original Nickname", user.getNickname()); // Should remain unchanged
        verify(userRepository, never()).save(user);
    }



    @Test
    void shouldThrowExceptionWhenUserNotFound() {
        // Given
        UpdateUserRequest request = new UpdateUserRequest();
        request.setNickname("New Nickname");
        
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, 
            () -> userManagementService.updateUser(userId, request));
        
        assertTrue(exception.getMessage().contains("User"));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void shouldThrowExceptionWhenUpdatingToExistingNickname() {
        // Given
        UpdateUserRequest request = new UpdateUserRequest();
        request.setNickname("Existing Nickname");

        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(userRepository.existsByNickname("Existing Nickname")).thenReturn(true);

        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class,
            () -> userManagementService.updateUser(userId, request));

        assertEquals("Nickname already exists", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }
}
