package com.pgs.service;

import com.pgs.entity.User;
import com.pgs.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuditServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private AuditService auditService;

    @Test
    void shouldGetSystemUserId() {
        // Given
        UUID systemUserId = UUID.randomUUID();
        User systemUser = User.builder()
            .id(systemUserId)
            .username("SYSTEM")
            .nickname("System User")
            .build();

        when(userRepository.findByUsername("SYSTEM")).thenReturn(Optional.of(systemUser));

        // When
        UUID result = auditService.getSystemUserId();

        // Then
        assertEquals(systemUserId, result);
        verify(userRepository).findByUsername("SYSTEM");
    }

    @Test
    void shouldCacheSystemUserId() {
        // Given
        UUID systemUserId = UUID.randomUUID();
        User systemUser = User.builder()
            .id(systemUserId)
            .username("SYSTEM")
            .nickname("System User")
            .build();

        when(userRepository.findByUsername("SYSTEM")).thenReturn(Optional.of(systemUser));

        // When
        UUID result1 = auditService.getSystemUserId();
        UUID result2 = auditService.getSystemUserId();

        // Then
        assertEquals(systemUserId, result1);
        assertEquals(systemUserId, result2);
        verify(userRepository, times(1)).findByUsername("SYSTEM"); // Should only call once due to caching
    }

    @Test
    void shouldThrowExceptionWhenSystemUserNotFound() {
        // Given
        when(userRepository.findByUsername("SYSTEM")).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            auditService.getSystemUserId();
        });

        assertTrue(exception.getMessage().contains("System user not found"));
    }
}
