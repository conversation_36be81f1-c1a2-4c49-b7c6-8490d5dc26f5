package com.pgs.service;

import com.pgs.entity.AuditLog;
import com.pgs.entity.User;
import com.pgs.repository.AuditLogRepository;
import com.pgs.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.pgs.security.UserPrincipal;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final AuditLogRepository auditLogRepository;
    private final UserRepository userRepository;

    private UUID systemUserId = null;

    @Async
    @Transactional
    public void logUserAction(UUID userId, String action, String targetType, UUID targetId, String description) {
        try {
            User user = userId != null ? userRepository.findById(userId).orElse(null) : null;
            
            AuditLog auditLog = AuditLog.builder()
                .user(user)
                .action(action)
                .targetType(targetType)
                .targetId(targetId)
                .description(description)
                .ipAddress(getClientIpAddress())
                .userAgent(getUserAgent())
                .timestamp(LocalDateTime.now())
                .build();

            auditLogRepository.save(auditLog);
            
        } catch (Exception e) {
            log.error("Failed to log audit action: {}", action, e);
        }
    }

    /**
     * Gets the system user ID for automated processes.
     * Caches the result to avoid repeated database queries.
     */
    public UUID getSystemUserId() {
        if (systemUserId == null) {
            systemUserId = userRepository.findByUsername("SYSTEM")
                .map(User::getId)
                .orElseThrow(() -> new RuntimeException("System user not found. Please ensure data initialization has run."));
        }
        return systemUserId;
    }

    /**
     * Logs a system action performed by automated processes.
     */
    @Async
    @Transactional
    public void logSystemAction(String action, String targetType, UUID targetId, String description) {
        logUserAction(getSystemUserId(), action, targetType, targetId, description);
    }

    /**
     * Gets the current authenticated user ID, or system user ID if no user is authenticated.
     */
    public UUID getCurrentUserIdOrSystem() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() &&
                authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                return userPrincipal.getId();
            }
        } catch (Exception e) {
            log.debug("Could not get current user from security context, using system user", e);
        }
        return getSystemUserId();
    }

    @Transactional(readOnly = true)
    public Page<AuditLog> getAuditLogs(UUID userId, String action, LocalDateTime fromTime,
                                      LocalDateTime toTime, Pageable pageable) {
        return auditLogRepository.findWithFilters(userId, action, fromTime, toTime, pageable);
    }

    @Transactional(readOnly = true)
    public Page<Object> getAuditLogs(String userIdStr, String type, LocalDateTime from, LocalDateTime to, Pageable pageable) {
        UUID userId = userIdStr != null ? UUID.fromString(userIdStr) : null;
        return auditLogRepository.findWithFilters(userId, type, from, to, pageable)
            .map(this::mapAuditLogToResponse);
    }

    @Transactional(readOnly = true)
    public Page<AuditLog> getUserAuditLogs(UUID userId, Pageable pageable) {
        return auditLogRepository.findByUserId(userId, pageable);
    }

    public String exportAuditLogs(String userIdStr, String type, LocalDateTime from, LocalDateTime to) {
        // In a real implementation, this would generate a CSV file and return a download link
        // For now, we'll return a placeholder
        log.info("Exporting audit logs with filters - userId: {}, type: {}, from: {}, to: {}",
            userIdStr, type, from, to);

        // Generate a unique filename
        String filename = "audit_logs_" + System.currentTimeMillis() + ".csv";

        // todo: implement the following
        // In production, you would:
        // 1. Query the audit logs with the filters
        // 2. Generate a CSV file
        // 3. Store it in a temporary location or cloud storage
        // 4. Return the download URL

        return "/downloads/" + filename;
    }

    private Object mapAuditLogToResponse(AuditLog auditLog) {
        return new Object() {
            public String getId() { return auditLog.getId().toString(); }
            public String getUserId() {
                return auditLog.getUser() != null ? auditLog.getUser().getId().toString() : null;
            }
            public String getUsername() {
                return auditLog.getUser() != null ? auditLog.getUser().getUsername() : null;
            }
            public String getAction() { return auditLog.getAction(); }
            public String getTargetType() { return auditLog.getTargetType(); }
            public String getTargetId() {
                return auditLog.getTargetId() != null ? auditLog.getTargetId().toString() : null;
            }
            public String getDescription() { return auditLog.getDescription(); }
            public String getIpAddress() {
                return auditLog.getIpAddress() != null ? auditLog.getIpAddress().getHostAddress() : null;
            }
            public String getUserAgent() { return auditLog.getUserAgent(); }
            public LocalDateTime getTimestamp() { return auditLog.getTimestamp(); }
        };
    }

    private InetAddress getClientIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                    return InetAddress.getByName(xForwardedFor.split(",")[0].trim());
                }
                String xRealIp = request.getHeader("X-Real-IP");
                if (xRealIp != null && !xRealIp.isEmpty()) {
                    return InetAddress.getByName(xRealIp);
                }
                return InetAddress.getByName(request.getRemoteAddr());
            }
        } catch (UnknownHostException e) {
            log.warn("Could not determine client IP address", e);
        }
        return null;
    }

    private String getUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("Could not determine user agent", e);
        }
        return null;
    }
}
