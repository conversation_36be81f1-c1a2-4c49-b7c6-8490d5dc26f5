package com.pgs.service;

import com.pgs.dto.user.*;
import com.pgs.entity.Role;
import com.pgs.entity.User;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.RoleRepository;
import com.pgs.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserManagementService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuditService auditService;
    private final TwoFactorAuthService twoFactorAuthService;

    @Transactional
    public UserResponse createUser(CreateUserRequest request) {
        // Check if username already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BadRequestException("Username already exists");
        }

        // Validate nickname is not null or empty
        if (request.getNickname() == null || request.getNickname().trim().isEmpty()) {
            throw new BadRequestException("Nickname is required");
        }

        // Check if nickname already exists
        if (userRepository.existsByNickname(request.getNickname().trim())) {
            throw new BadRequestException("Nickname already exists");
        }

        // Validate and get roles
        Set<Role> roles = validateAndGetRoles(request.getRoles());

        // Create user
        User user = User.builder()
            .username(request.getUsername())
            .nickname(request.getNickname().trim())
            .passwordHash(passwordEncoder.encode(request.getPassword()))
            .isActive(true)
            .build();

        // Assign roles
        roles.forEach(user::addRole);

        User savedUser = userRepository.save(user);

        auditService.logUserAction(auditService.getCurrentUserIdOrSystem(), "USER_CREATED", "USER", savedUser.getId(),
            "User created: " + savedUser.getUsername());

        return mapToUserResponse(savedUser);
    }

    @Transactional(readOnly = true)
    public Page<UserResponse> getAllUsers(Pageable pageable) {
        return userRepository.findAllActive(pageable)
            .map(this::mapToUserResponse);
    }

    @Transactional(readOnly = true)
    public UserResponse getUserById(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", userId));
        
        return mapToUserResponse(user);
    }

    @Transactional
    public UserResponse updateUser(UUID userId, UpdateUserRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", userId));

        boolean updated = false;

        // Update nickname if provided
        if (request.getNickname() != null && !request.getNickname().trim().isEmpty()
            && !request.getNickname().trim().equals(user.getNickname())) {
            // Check if new nickname already exists
            if (userRepository.existsByNickname(request.getNickname().trim())) {
                throw new BadRequestException("Nickname already exists");
            }
            user.setNickname(request.getNickname().trim());
            updated = true;
        }

        if (request.getIsActive() != null && !request.getIsActive().equals(user.getIsActive())) {
            user.setIsActive(request.getIsActive());
            updated = true;
        }

        if (request.getRoles() != null) {
            Set<Role> newRoles = validateAndGetRoles(request.getRoles());
            user.getRoles().clear();
            newRoles.forEach(user::addRole);
            updated = true;
        }

        if (updated) {
            User savedUser = userRepository.save(user);
            auditService.logUserAction(auditService.getCurrentUserIdOrSystem(), "USER_UPDATED", "USER", userId,
                "User updated: " + user.getUsername());
            return mapToUserResponse(savedUser);
        }

        return mapToUserResponse(user);
    }

    @Transactional
    public void deleteUser(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", userId));

        user.setIsActive(false);
        userRepository.save(user);

        auditService.logUserAction(auditService.getCurrentUserIdOrSystem(), "USER_DELETED", "USER", userId,
            "User deactivated: " + user.getUsername());
    }

    @Transactional
    public void changePassword(UUID userId, ChangePasswordRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", userId));

        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPasswordHash())) {
            throw new BadRequestException("Current password is incorrect");
        }

        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        auditService.logUserAction(userId, "PASSWORD_CHANGED", "USER", userId, 
            "User changed password");
    }

    private Set<Role> validateAndGetRoles(Set<String> roleNames) {
        Set<Role> roles = roleRepository.findByNameIn(roleNames);
        
        if (roles.size() != roleNames.size()) {
            Set<String> foundRoleNames = roles.stream()
                .map(Role::getName)
                .collect(Collectors.toSet());
            
            Set<String> notFoundRoles = roleNames.stream()
                .filter(name -> !foundRoleNames.contains(name))
                .collect(Collectors.toSet());
                
            throw new BadRequestException("Roles not found: " + notFoundRoles);
        }
        
        return roles;
    }

    private UserResponse mapToUserResponse(User user) {
        return UserResponse.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .nickname(user.getNickname())
            .isActive(user.getIsActive())
            .twoFactorEnabled(twoFactorAuthService.isTwoFactorEnabled(user))
            .roles(user.getRoles().stream()
                .map(Role::getName)
                .collect(Collectors.toSet()))
            .createdAt(user.getCreatedAt())
            .updatedAt(user.getUpdatedAt())
            .build();
    }
}
