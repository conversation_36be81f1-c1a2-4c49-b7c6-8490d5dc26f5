package com.pgs.service;

import com.pgs.entity.Role;
import com.pgs.entity.User;
import com.pgs.repository.RoleRepository;
import com.pgs.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    @Value("${pgs.security.admin.username}")
    private String adminUsername;

    @Value("${pgs.security.admin.password}")
    private String adminPassword;

    @Override
    @Transactional
    public void run(String... args) {
        createDefaultRoles();
        createDefaultAdminUser();
        createSystemUser();
    }

    private void createDefaultRoles() {
        if (!roleRepository.existsByName("ADMIN")) {
            Role adminRole = Role.builder()
                .name("ADMIN")
                .build();
            roleRepository.save(adminRole);
            log.info("Created default ADMIN role");
        }

        if (!roleRepository.existsByName("USER")) {
            Role userRole = Role.builder()
                .name("USER")
                .build();
            roleRepository.save(userRole);
            log.info("Created default USER role");
        }

        if (!roleRepository.existsByName("OPERATOR")) {
            Role operatorRole = Role.builder()
                .name("OPERATOR")
                .build();
            roleRepository.save(operatorRole);
            log.info("Created default OPERATOR role");
        }
    }

    private void createDefaultAdminUser() {
        if (!userRepository.existsByUsername(adminUsername)) {
            Role adminRole = roleRepository.findByName("ADMIN")
                .orElseThrow(() -> new RuntimeException("ADMIN role not found"));

            User adminUser = User.builder()
                .username(adminUsername)
                .nickname("Administrator")
                .passwordHash(passwordEncoder.encode(adminPassword))
                .isActive(true)
                .build();

            adminUser.addRole(adminRole);
            userRepository.save(adminUser);

            log.info("Created default admin user: {}", adminUsername);
        }
    }

    private void createSystemUser() {
        String systemUsername = "SYSTEM";
        if (!userRepository.existsByUsername(systemUsername)) {
            Role operatorRole = roleRepository.findByName("OPERATOR")
                .orElseThrow(() -> new RuntimeException("OPERATOR role not found"));

            User systemUser = User.builder()
                .username(systemUsername)
                .nickname("System User")
                .passwordHash(passwordEncoder.encode("SYSTEM_PASSWORD_NOT_USED"))
                .isActive(true)
                .build();

            systemUser.addRole(operatorRole);
            userRepository.save(systemUser);

            log.info("Created system user for automated processes");
        }
    }
}
