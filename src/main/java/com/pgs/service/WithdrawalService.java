package com.pgs.service;

import com.pgs.config.BusinessConfig;
import com.pgs.dto.withdrawal.WithdrawalRequestDto;
import com.pgs.dto.withdrawal.WithdrawalResponse;
import com.pgs.entity.BankAccount;
import com.pgs.entity.User;
import com.pgs.entity.WithdrawalRequest;
import com.pgs.enums.WithdrawalStatus;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.UserRepository;
import com.pgs.repository.WithdrawalRequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class WithdrawalService {

    private final WithdrawalRequestRepository withdrawalRequestRepository;
    private final BankAccountRepository bankAccountRepository;
    private final UserRepository userRepository;
    private final BusinessConfig businessConfig;
    private final AuditService auditService;

    /**
     * Creates a new withdrawal request, reserves the amount, and deducts from bank account balance.
     * The frozen amount and balance will be automatically restored by RequestExpirationScheduler if the request expires.
     */
    @Transactional
    public WithdrawalResponse createWithdrawalRequest(WithdrawalRequestDto request) {
        // Validate user exists
        //todo: no need to validate here, the userId in DepositRequestDto is not the user from our system, it is the user for the merchant system
        User user = userRepository.findById(UUID.fromString(request.getUserId()))
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", request.getUserId()));

        // Check if order number already exists
        if (withdrawalRequestRepository.findByOrderNumber(request.getOrderNumber()).isPresent()) {
            throw new BadRequestException("Order number already exists");
        }

        // Find available bank account for withdrawal
        List<BankAccount> availableAccounts = bankAccountRepository.findAvailableForWithdrawal(request.getAmount());
        if (availableAccounts.isEmpty()) {
            throw new BadRequestException("No bank account available for this withdrawal amount");
        }

        // Select the first available account
        BankAccount selectedAccount = availableAccounts.get(0);

        // Create withdrawal request
        WithdrawalRequest withdrawalRequest = WithdrawalRequest.builder()
            .user(user)
            .currency(request.getCurrency())
            .merchantCode(request.getMerchantCode())
            .orderNumber(request.getOrderNumber())
            .extendParam(request.getExtendParam())
            .bankAccount(selectedAccount)
            .amount(request.getAmount())
            .targetAccountNumber(request.getTargetAccount().getAccountNumber())
            .targetBankName(request.getTargetAccount().getBankName())
            .targetAccountHolder(request.getTargetAccount().getAccountHolderName())
            .status(WithdrawalStatus.PENDING)
            .instructionSent(false)
            .expiresAt(LocalDateTime.now().plusMinutes(businessConfig.getWithdrawal().getExpiryTimeoutMinutes()))
            .build();

        WithdrawalRequest savedRequest = withdrawalRequestRepository.save(withdrawalRequest);

        // Update bank account frozen daily usage and balance (reserve the amount)
        selectedAccount.freezeWithdrawalAmount(request.getAmount());
        selectedAccount.setCurrentBalance(selectedAccount.getCurrentBalance().subtract(request.getAmount()));
        bankAccountRepository.save(selectedAccount);

        // Log as system action since this is a system-initiated process
        auditService.logSystemAction("WITHDRAWAL_REQUEST_CREATED", "WITHDRAWAL_REQUEST",
            savedRequest.getId(), "Withdrawal request created: " + request.getOrderNumber() + " for user: " + user.getUsername());

        log.info("Withdrawal request created: {} for amount: {} assigned to bank: {}", 
            request.getOrderNumber(), request.getAmount(), selectedAccount.getBankName());

        // todo: should have a scheduler job running every 1 minute, to check if any request is expired, and mark it as failed
        // TODO: TAK - Send instruction to robot via WebSocket
        // This would be implemented when WebSocket support is added

        return mapToWithdrawalResponse(savedRequest);
    }

    private WithdrawalResponse mapToWithdrawalResponse(WithdrawalRequest request) {
        return WithdrawalResponse.builder()
            .requestId(request.getId())
            .orderNumber(request.getOrderNumber())
            .amount(request.getAmount())
            .currency(request.getCurrency())
            .status(request.getStatus().name())
            .targetAccountNumber(request.getTargetAccountNumber())
            .targetBankName(request.getTargetBankName())
            .targetAccountHolder(request.getTargetAccountHolder())
            .qrCode(request.getQrCode())
            .expiresAt(request.getExpiresAt())
            .createdAt(request.getCreatedAt())
            .build();
    }
}
