package com.pgs.service.scheduler;

import com.pgs.entity.BankAccount;
import com.pgs.entity.DepositRequest;
import com.pgs.entity.WithdrawalRequest;
import com.pgs.enums.DepositStatus;
import com.pgs.enums.WithdrawalStatus;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.DepositRequestRepository;
import com.pgs.repository.WithdrawalRequestRepository;
import com.pgs.service.AuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Scheduled service to check for expired deposit and withdrawal requests every minute.
 * Handles status updates and releases frozen amounts when requests expire.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RequestExpirationScheduler {

    private final DepositRequestRepository depositRequestRepository;
    private final WithdrawalRequestRepository withdrawalRequestRepository;
    private final BankAccountRepository bankAccountRepository;
    private final AuditService auditService;

    /**
     * Scheduled job that runs every minute to check for expired requests.
     * Uses fixedRate = 60000 (60 seconds) for every-minute execution.
     */
    @Scheduled(fixedRate = 60000)
    @Transactional
    public void processExpiredRequests() {
        log.debug("Starting expired requests processing job");
        
        try {
            LocalDateTime now = LocalDateTime.now();
            
            int expiredDeposits = processExpiredDepositRequests(now);
            int expiredWithdrawals = processExpiredWithdrawalRequests(now);
            
            int totalExpired = expiredDeposits + expiredWithdrawals;
            
            if (totalExpired > 0) {
                log.info("Processed {} expired requests: {} deposits, {} withdrawals", 
                    totalExpired, expiredDeposits, expiredWithdrawals);
                
                // Log the operation for audit purposes
                auditService.logSystemAction("EXPIRED_REQUESTS_PROCESSED", "SYSTEM", null,
                    "Processed " + totalExpired + " expired requests (" + expiredDeposits +
                    " deposits, " + expiredWithdrawals + " withdrawals)");
            } else {
                log.debug("No expired requests found");
            }
            
        } catch (Exception e) {
            log.error("Error occurred during expired requests processing job", e);
            
            // Log the error for audit purposes
            auditService.logSystemAction("EXPIRED_REQUESTS_PROCESSING_ERROR", "SYSTEM", null,
                "Expired requests processing job failed: " + e.getMessage());
            
            // Re-throw to ensure the error is properly handled by Spring's scheduler
            throw e;
        }
    }
    
    /**
     * Processes expired deposit requests.
     * Changes status from PENDING to UNPAID and releases frozen amounts.
     */
    private int processExpiredDepositRequests(LocalDateTime now) {
        List<DepositRequest> expiredDeposits = depositRequestRepository
            .findExpiredByStatus(DepositStatus.PENDING, now);
        
        if (expiredDeposits.isEmpty()) {
            return 0;
        }
        
        log.info("Found {} expired deposit requests", expiredDeposits.size());
        
        for (DepositRequest request : expiredDeposits) {
            try {
                // Update request status
                request.setStatus(DepositStatus.UNPAID);
                
                // Release frozen amount from assigned bank account
                BankAccount bankAccount = request.getAssignedBankAccount();
                if (bankAccount != null) {
                    bankAccount.releaseFrozenDepositAmount(request.getAmount());
                    bankAccountRepository.save(bankAccount);
                    
                    log.debug("Released frozen deposit amount {} for bank account {} (request: {})", 
                        request.getAmount(), bankAccount.getAccountNumber(), request.getOrderNumber());
                }
                
                // Save the updated request
                depositRequestRepository.save(request);
                
                // Log individual request expiration
                auditService.logSystemAction("DEPOSIT_REQUEST_EXPIRED", "DEPOSIT_REQUEST",
                    request.getId(), "Deposit request expired: " + request.getOrderNumber() +
                    ", amount: " + request.getAmount());
                
                log.info("Expired deposit request: {} (amount: {})", 
                    request.getOrderNumber(), request.getAmount());
                
            } catch (Exception e) {
                log.error("Error processing expired deposit request: {} (ID: {})", 
                    request.getOrderNumber(), request.getId(), e);
                // Continue processing other requests even if one fails
            }
        }
        
        return expiredDeposits.size();
    }
    
    /**
     * Processes expired withdrawal requests.
     * Changes status from PENDING to EXPIRED, releases frozen amounts, and restores balance.
     */
    private int processExpiredWithdrawalRequests(LocalDateTime now) {
        List<WithdrawalRequest> expiredWithdrawals = withdrawalRequestRepository
            .findExpiredByStatus(WithdrawalStatus.PENDING, now);
        
        if (expiredWithdrawals.isEmpty()) {
            return 0;
        }
        
        log.info("Found {} expired withdrawal requests", expiredWithdrawals.size());
        
        for (WithdrawalRequest request : expiredWithdrawals) {
            try {
                // Update request status
                request.setStatus(WithdrawalStatus.EXPIRED);
                
                // Release frozen amount and restore balance in bank account
                BankAccount bankAccount = request.getBankAccount();
                if (bankAccount != null) {
                    bankAccount.releaseFrozenWithdrawalAmount(request.getAmount());
                    // Restore the balance that was deducted when the request was created
                    bankAccount.setCurrentBalance(bankAccount.getCurrentBalance().add(request.getAmount()));
                    bankAccountRepository.save(bankAccount);
                    
                    log.debug("Released frozen withdrawal amount {} and restored balance for bank account {} (request: {})", 
                        request.getAmount(), bankAccount.getAccountNumber(), request.getOrderNumber());
                }
                
                // Save the updated request
                withdrawalRequestRepository.save(request);
                
                // Log individual request expiration
                auditService.logSystemAction("WITHDRAWAL_REQUEST_EXPIRED", "WITHDRAWAL_REQUEST",
                    request.getId(), "Withdrawal request expired: " + request.getOrderNumber() +
                    ", amount: " + request.getAmount());
                
                log.info("Expired withdrawal request: {} (amount: {})", 
                    request.getOrderNumber(), request.getAmount());
                
            } catch (Exception e) {
                log.error("Error processing expired withdrawal request: {} (ID: {})", 
                    request.getOrderNumber(), request.getId(), e);
                // Continue processing other requests even if one fails
            }
        }
        
        return expiredWithdrawals.size();
    }
}
