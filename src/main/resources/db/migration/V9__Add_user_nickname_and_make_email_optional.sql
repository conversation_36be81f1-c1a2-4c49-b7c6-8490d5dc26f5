-- Add nickname column to users table and remove email column
ALTER TABLE users
ADD COLUMN nickname VA<PERSON>HAR(100) NOT NULL DEFAULT 'temp_nickname';

-- Update existing users to have a unique nickname based on username with a suffix to ensure uniqueness
UPDATE users
SET nickname = username || '_nick_' || id::text
WHERE nickname = 'temp_nickname';

-- Remove the default value from nickname column after updating existing records
ALTER TABLE users
ALTER COLUMN nickname DROP DEFAULT;

-- Add unique constraint to nickname column
ALTER TABLE users
ADD CONSTRAINT users_nickname_unique UNIQUE (nickname);

-- Remove unique constraint from email column
ALTER TABLE users
DROP CONSTRAINT IF EXISTS users_email_key;

-- Drop the email column entirely
ALTER TABLE users
DROP COLUMN IF EXISTS email;
